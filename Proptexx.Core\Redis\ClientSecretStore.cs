using System.Text.Json;
using <PERSON>pper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Npgsql;
using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public interface IClientSecretStore
{
    Task<ClientSecretModel?> GetAsync(string secretId);

    Task SyncAsync(CancellationToken stoppingToken);
}

public class ClientSecretStore : IClientSecretStore
{
    private const string KeyPrefix = "client_secret";

    private readonly ILogger _logger;
    private readonly IServiceProvider _services;
    private readonly IDatabase _redis;

    public ClientSecretStore(ILogger<ClientSecretStore> logger, IServiceProvider services)
    {
        _logger = logger;
        _services = services;
        
        var connectionMultiplexer = _services.GetRequiredService<IConnectionMultiplexer>();
        _redis = connectionMultiplexer.GetDatabase();
    }

    public async Task<ClientSecretModel?> GetAsync(string secretId)
    {
        _logger.LogDebug("Looking up client secret for secretId: {SecretId}", secretId);

        var value = await _redis.HashGetAsync(KeyPrefix, secretId);
        if (value.IsNullOrEmpty)
        {
            // Check how many entries are in the hash
            var hashLength = await _redis.HashLengthAsync(KeyPrefix);
            _logger.LogWarning("Client secret not found for secretId: {SecretId}. Total entries in cache: {HashLength}",
                secretId, hashLength);
            return null;
        }

        _logger.LogDebug("Found client secret for secretId: {SecretId}", secretId);
        return JsonSerializer.Deserialize<ClientSecretModel>(value.ToString());
    }

    public async Task SyncAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Starting client secret sync to Redis");

            var dataSource = _services.GetRequiredService<NpgsqlDataSource>();
            await using var npgsql = await dataSource.OpenConnectionAsync(stoppingToken);
            var enumerable = await npgsql.QueryAsync<ClientSecretModel>(ClientSecretModel.Sql);

            var clientSecrets = enumerable.ToArray();
            _logger.LogInformation("Retrieved {Count} client secrets from database", clientSecrets.Length);

            if (clientSecrets.Length == 0)
            {
                _logger.LogWarning("No client secrets found in database. This might indicate a problem with the query or database state.");
                return;
            }

            var fields = clientSecrets
                .Select(x => new HashEntry(
                    x.SecretId.ToString(),
                    JsonSerializer.Serialize(x)))
                .ToArray();

            await _redis.HashSetAsync(KeyPrefix, fields);
            _logger.LogInformation("Successfully synced {Count} client secrets to Redis", fields.Length);
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"System exception in {nameof(SyncAsync)}");
        }
    }
}

public class ClientSecretModel
{
    public static string Sql = @" 
            select c.id as client_id,
                   cs.id as secret_id,
                   c.name as client_name,
                   w.id as workspace_id,
                   w.name as workspace_name,
                   w.title as workspace_title,
                   c.scopes,
                   c.session_lifespan,
                   c.issue_token
            from core.workspace w
            join core.client c on w.id = c.workspace_id
            join core.client_secret cs on c.id = cs.client_id and cs.expired_at is null or cs.expired_at > current_timestamp
            where c.cancelled_at is null or c.cancelled_at > current_timestamp;
        ";
        
    public Guid ClientId { get; init; }
        
    public Guid SecretId { get; init; }
    
    public Guid WorkspaceId { get; init; }
    
    public required string WorkspaceName { get; init; }
    
    public required string WorkspaceTitle { get; init; }
    
    public required string ClientName { get; init; }
    
    public string? Scopes { get; init; }
    
    public int? TokenLifespan { get; init; }

    public int? SessionLifespan { get; init; }
    
    public bool IssueToken { get; init; }
}