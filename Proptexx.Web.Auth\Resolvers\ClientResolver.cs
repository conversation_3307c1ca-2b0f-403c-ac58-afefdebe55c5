using Proptexx.Core.Redis;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class ClientResolver : IClientResolver
{
    private readonly IClientSecretStore _clientSecretStore;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ClientResolver> _logger;

    public ClientResolver(IClientSecretStore clientSecretStore, IConfiguration configuration, ILogger<ClientResolver> logger)
    {
        _clientSecretStore = clientSecretStore;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<IClientResolverResult> ResolveAsync(string identifier, string? accountId, params string[] scopes)
    {
        _logger.LogDebug("Attempting to resolve client with identifier: {Identifier}", identifier);

        var clientSecret = await _clientSecretStore.GetAsync(identifier);
        if (clientSecret == null)
        {
            _logger.LogWarning("Client secret not found for identifier: {Identifier}", identifier);
            throw new UnauthorizedAccessException($"Client secret not found for identifier: {identifier}");
        }

        _logger.LogDebug("Successfully resolved client secret for identifier: {Identifier}, ClientId: {ClientId}",
            identifier, clientSecret.ClientId);

        var result = new ClientResolverResult
        {
            ClientId = clientSecret.ClientId.ToString(),
            IssueCookie = clientSecret.IssueToken,
            AccessTokenTtl = clientSecret.TokenLifespan ?? _configuration.GetValue<int>("Proptexx:AccessTokenTtl"),
            SessionTokenTtl = clientSecret.SessionLifespan ?? _configuration.GetValue<int>("Proptexx:SessionTokenTtl")
        };

        result.AdditionalClaims.Add("workspaceId", clientSecret.WorkspaceId.ToString());
        result.AdditionalClaims.Add("workspaceName", clientSecret.WorkspaceName);
        result.AdditionalClaims.Add("workspaceTitle", clientSecret.WorkspaceTitle);
        result.AdditionalClaims.Add("clientName", clientSecret.ClientName);
        result.AdditionalClaims.Add("clientSecretId", clientSecret.SecretId.ToString());
        return result;
    }
}

public class ClientResolverResult : IClientResolverResult
{
    public required string ClientId { get; init; }

    public int? AccessTokenTtl { get; init; }

    public int? SessionTokenTtl { get; init; }

    public bool? IssueCookie { get; init; }

    public Dictionary<string, string> AdditionalClaims { get; } = [];
}